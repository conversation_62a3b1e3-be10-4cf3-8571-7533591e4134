<?php

namespace App\Modules\CollectDebt\Actions\RequestPayment\CutOffRequestPaymentAction;

use App\Modules\CollectDebt\Model\RequestPayment;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebtGateway\Repositories\CollectDebtGatewayRepository;
use App\Modules\CollectDebt\DTOs\RequestPaymentReceipt\CreateRequestPaymentReceiptDTO;
use App\Modules\CollectDebt\Actions\RequestPayment\CheckRequestPaymentAction\CheckRequestPaymentAction;
use App\Modules\CollectDebt\Actions\RequestPayment\CancelRequestPaymentAction\CancelRequestPaymentAction;
use App\Modules\CollectDebt\Actions\RequestPayment\CheckRequestPaymentAction\CheckRequestPaymentVAAction;
use App\Modules\CollectDebt\Actions\RequestPaymentReceipt\CreateRequestPaymentReceiptAction\CreateRequestPaymentReceiptAction;

class CutOffRequestPaymentAction
{
	private const MAX_ITERATIONS = 40;
	private const EMPTY_RESULT = 'EMPTY';
	private const PAYMENT_METHOD_MPOS = 'MPOS';
	private const PAYMENT_METHOD_VIRTUAL_ACCOUNT = 'VIRTUALACCOUNT';

	/** @var array */
	private $processedIds = [];

	/** @var array */
	private $excludedIds = [];

	/** @var CollectDebtGatewayRepository */
	public $mposCollectDebtGatewayRepo;

	public function __construct(CollectDebtGatewayRepository $mposCollectDebtGatewayRepo)
	{
		$this->mposCollectDebtGatewayRepo = $mposCollectDebtGatewayRepo;
	}

	/**
	 * @return array
	 */
	public function run()
	{
		try {
			for ($i = 1; $i < self::MAX_ITERATIONS; $i++) {
				$requestPayment = $this->processCutOffRequestPayment();

				if ($requestPayment === self::EMPTY_RESULT) {
					break;
				}

				$this->processedIds[] = $requestPayment->partner_request_id;
			}
		} catch (\Throwable $th) {
			throw $th;
		}

		return $this->processedIds;
	}

	public function processCutOffRequestPayment()
	{
		$requestPayment = $this->getNextExpiredRequestPayment();

		if (!$requestPayment) {
			return self::EMPTY_RESULT;
		}

		$this->excludedIds[] = $requestPayment->id;

		// Nếu kênh trích là mpos
		if ($requestPayment->payment_method_code === self::PAYMENT_METHOD_MPOS) {
			return $this->processMposPayment($requestPayment);
		}

		// Nếu kênh trích là VA
		if ($requestPayment->payment_method_code === self::PAYMENT_METHOD_VIRTUAL_ACCOUNT) {
			return $this->processVirtualAccountPayment($requestPayment);
		}

		return $requestPayment;
	}

	/**
	 * @return RequestPayment|null
	 */
	private function getNextExpiredRequestPayment()
	{
		$query = RequestPayment::query()
			->where('status_payment', '!=', CollectDebtEnum::RP_STT_PAYMENT_RECEIVED)
			->where('time_expired', '<=', now()->timestamp)
			->doesntHave('requestPaymentReceipt');

		if (!empty($this->excludedIds)) {
			$query->whereNotIn('id', $this->excludedIds);
		}

		return $query->orderByRaw(request('orderByRaw', 'id asc'))->first();
	}

	/**
	 * @param RequestPayment $requestPayment
	 * @return mixed
	 */
	private function processMposPayment(RequestPayment $requestPayment)
	{
		// Lỗi gì đó mà không thể đẩy lệnh trích sang đối tác
		if (empty($requestPayment->partner_transaction_id)) {
			return $this->handleEmptyPartnerTransactionId($requestPayment);
		}

		$checkResult = app(CheckRequestPaymentAction::class)->executeCheck($requestPayment);
		$debtStatus = app(CheckRequestPaymentAction::class)->getDebitCmdStatus($checkResult);

		switch ($debtStatus) {
			// Hủy thành công -> giải phóng lệnh trích
			case 'CANCEL':
			case 'NOTFOUND':
				return $this->createReceiptAndReturn($requestPayment, 0);

				// chưa có kết quả trích
			case 'PENDING':
				return $this->handlePendingStatus($requestPayment);

				// lệnh hết hạn -> gọi cancel và không quan tâm cancel được hay không
			case 'EXPIRED':
				return $this->handleExpiredStatus($requestPayment);

				// gọi sang mpos có vấn đề gì đó mà không được
			case 'TIMEOUT':
				return $this->handleTimeoutStatus($requestPayment);

				// trích thành công -> giải phóng lệnh trích
			case 'APPROVED':
				return $this->handleApprovedStatus($requestPayment, $checkResult);

				// những trường hợp khác, gọi cancel rồi bắt trên kết quả
			default:
				return $this->handleDefaultStatus($requestPayment);
		}
	}

	/**
	 * @param RequestPayment $requestPayment
	 * @return mixed
	 */
	private function processVirtualAccountPayment(RequestPayment $requestPayment)
	{
		$checkVaResult = app(CheckRequestPaymentVAAction::class)->executeCheck($requestPayment);
		$isPaidVA = app(CheckRequestPaymentVAAction::class)->isPaidVA($checkVaResult);

		$amountSuccess = $isPaidVA ? $requestPayment->amount_request : 0;
		$description = $isPaidVA ? '' : 'Cutoff lệnh VA không có thông tin';

		$dto = CreateRequestPaymentReceiptDTO::fromRequestPayment(
			$requestPayment,
			$amountSuccess,
			CollectDebtEnum::RP_RECEIPT_APPROVED
		);

		$dto->description = $description;
		return app(CreateRequestPaymentReceiptAction::class)->run($dto);
	}

	/**
	 * @param RequestPayment $requestPayment
	 * @param int $amount
	 * @return RequestPayment
	 */
	private function createReceiptAndReturn(RequestPayment $requestPayment, $amount)
	{
		$dto = CreateRequestPaymentReceiptDTO::fromRequestPayment(
			$requestPayment,
			$amount,
			CollectDebtEnum::RP_RECEIPT_APPROVED
		);
		app(CreateRequestPaymentReceiptAction::class)->run($dto);
		return $requestPayment;
	}

	/**
	 * @param RequestPayment $requestPayment
	 * @return RequestPayment
	 */
	private function handlePendingStatus(RequestPayment $requestPayment)
	{
		// Không có thông tin -> gọi hủy để kết thúc
		$cancelResult = app(CancelRequestPaymentAction::class)->run($requestPayment);
		if (CancelRequestPaymentAction::isCancelSuccess($cancelResult)) {
			return $this->createReceiptAndReturn($requestPayment, 0);
		}
		return $requestPayment;
	}

	/**
	 * @param RequestPayment $requestPayment
	 * @return RequestPayment
	 */
	private function handleExpiredStatus(RequestPayment $requestPayment)
	{
		// lệnh hết hạn -> gọi cancel và không quan tâm cancel được hay không
		app(CancelRequestPaymentAction::class)->run($requestPayment);
		return $this->createReceiptAndReturn($requestPayment, 0);
	}

	/**
	 * @param RequestPayment $requestPayment
	 * @return RequestPayment
	 */
	private function handleTimeoutStatus(RequestPayment $requestPayment)
	{
		// gọi sang mpos có vấn đề gì đó mà không được
		$cancelResult = app(CancelRequestPaymentAction::class)->run($requestPayment);
		if (CancelRequestPaymentAction::isCancelSuccess($cancelResult)) {
			return $this->createReceiptAndReturn($requestPayment, 0);
		}
		return $requestPayment;
	}

	/**
	 * @param RequestPayment $requestPayment
	 * @param array $checkResult
	 * @return RequestPayment
	 */
	private function handleApprovedStatus(RequestPayment $requestPayment, array $checkResult)
	{
		// trích thành công -> giải phóng lệnh trích
		$dto = CreateRequestPaymentReceiptDTO::fromRequestPayment(
			$requestPayment,
			$checkResult['data']['data']['debtRecoveryAmount'],
			CollectDebtEnum::RP_RECEIPT_APPROVED
		);
		app(CreateRequestPaymentReceiptAction::class)->run($dto);
		return $requestPayment;
	}

	/**
	 * @param RequestPayment $requestPayment
	 * @return RequestPayment
	 */
	private function handleDefaultStatus(RequestPayment $requestPayment)
	{
		// những trường hợp khác, gọi cancel rồi bắt trên kết quả
		$cancelResult = app(CancelRequestPaymentAction::class)->run($requestPayment);
		if (CancelRequestPaymentAction::isCancelSuccess($cancelResult)) {
			return $this->createReceiptAndReturn($requestPayment, 0);
		}
		return $requestPayment;
	}

	/**
	 * @param RequestPayment $requestPayment
	 * @return mixed
	 */
	public function handleEmptyPartnerTransactionId(RequestPayment $requestPayment)
	{
		$dto = CreateRequestPaymentReceiptDTO::fromRequestPayment(
			$requestPayment,
			0,
			CollectDebtEnum::RP_RECEIPT_APPROVED
		);
		$dto->description = 'Cutoff lệnh trích quá khứ không sang được đối tác trích nợ';
		return app(CreateRequestPaymentReceiptAction::class)->run($dto);
	}
}
